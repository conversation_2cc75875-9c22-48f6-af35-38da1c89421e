# CheckpointButton 组件

## 功能概述

CheckpointButton 组件为智能体模式和流程化智能体模式提供了checkpoint（检查点）功能，允许用户在开始智能体会话时创建一个本地历史检查点，并在需要时回滚到该检查点。

## 主要特性

1. **自动检测模式**: 只在智能体模式或流程化智能体模式下显示
2. **自动创建检查点**: 进入智能体模式时自动创建检查点
3. **简洁的UI设计**: 紧凑的按钮设计，包含图标和文字
4. **一键回滚**: 点击回滚按钮即可恢复到检查点状态

## 使用方式

### 在聊天界面中使用

组件已经集成到 `Chat.tsx` 中，会在聊天窗口的右上角显示：

```tsx
import { CheckpointButtonComponent } from "../../components/CheckpointButton";

// 在聊天界面中
<CheckpointButtonComponent />
```

### 状态管理

组件使用 Redux 管理状态：

- `state.session.checkpoint`: 存储当前检查点信息
- `state.session.mode`: 当前会话模式
- `state.session.structuredAgentWorkflow.isActive`: 是否在流程化智能体模式

### API 调用

组件通过以下 IDEA API 与本地历史系统交互：

- `createCheckpoint`: 创建新的检查点
- `rollbackToCheckpoint`: 回滚到指定检查点

## 组件结构

```
CheckpointButton/
├── CheckpointButton.tsx    # 主组件
├── CheckpointButton.test.tsx # 测试文件
├── index.ts               # 导出文件
└── README.md             # 文档
```

## 样式设计

- 使用 styled-components 进行样式管理
- 遵循应用的主题色彩系统
- 响应式设计，支持悬停效果
- 紧凑的布局，不占用过多空间

## 测试

运行测试：

```bash
npm test CheckpointButton
```

测试覆盖：
- 在不同模式下的显示/隐藏逻辑
- 检查点创建和回滚功能
- UI 交互测试
