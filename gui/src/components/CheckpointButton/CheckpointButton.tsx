import { ArrowUturnLeftIcon, BookmarkIcon } from "@heroicons/react/24/outline";
import React, { useCallback, useEffect } from "react";
import styled from "styled-components";
import { defaultBorderRadius, lightGray, vscForeground } from "..";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import {
  createCheckpointThunk,
  rollbackToCheckpointThunk,
} from "../../redux/thunks/checkpoint";
import { getFontSize } from "../../util";

const CheckpointContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 6px;
  padding: 4px 8px;
  border-radius: ${defaultBorderRadius};
  background-color: ${lightGray}22;
  border: 1px solid ${lightGray}44;
  font-size: ${getFontSize() - 2}px;
  color: ${lightGray};
`;

const CheckpointButton = styled.button`
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px 4px;
  border: none;
  border-radius: ${defaultBorderRadius};
  background-color: transparent;
  color: ${lightGray};
  cursor: pointer;
  font-size: ${getFontSize() - 2}px;

  &:hover {
    background-color: ${lightGray}33;
    color: ${vscForeground};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const CheckpointText = styled.span`
  font-size: ${getFontSize() - 2}px;
  color: ${lightGray};
`;

interface CheckpointButtonComponentProps {
  className?: string;
}

export const CheckpointButtonComponent: React.FC<
  CheckpointButtonComponentProps
> = ({ className }) => {
  const dispatch = useAppDispatch();
  const checkpoint = useAppSelector((state) => state.session.checkpoint);
  const mode = useAppSelector((state) => state.session.mode);
  const structuredAgentWorkflow = useAppSelector(
    (state) => state.session.structuredAgentWorkflow,
  );

  // 检查是否在智能体模式或流程化智能体模式
  const isInAgentMode = mode === "agent" || structuredAgentWorkflow.isActive;

  // 创建checkpoint
  const createCheckpoint = useCallback(() => {
    dispatch(createCheckpointThunk());
  }, [dispatch]);

  // 回滚到checkpoint
  const rollbackToCheckpoint = useCallback(() => {
    if (!checkpoint) return;
    dispatch(rollbackToCheckpointThunk(checkpoint.id));
  }, [dispatch, checkpoint]);

  // 在智能体模式下自动创建checkpoint
  useEffect(() => {
    if (isInAgentMode && !checkpoint) {
      createCheckpoint();
    }
  }, [isInAgentMode, checkpoint, createCheckpoint]);

  // 如果不在智能体模式下，不显示checkpoint按钮
  if (!isInAgentMode) {
    return null;
  }

  return (
    <CheckpointContainer className={className}>
      <BookmarkIcon width="12px" height="12px" />
      <CheckpointText>检查点</CheckpointText>

      {checkpoint ? (
        <CheckpointButton onClick={rollbackToCheckpoint} title="回滚到检查点">
          <ArrowUturnLeftIcon width="12px" height="12px" />
          <span>回滚</span>
        </CheckpointButton>
      ) : (
        <CheckpointButton onClick={createCheckpoint} title="创建检查点">
          <BookmarkIcon width="12px" height="12px" />
          <span>创建</span>
        </CheckpointButton>
      )}
    </CheckpointContainer>
  );
};

export default CheckpointButtonComponent;
