import { createAsyncThunk } from "@reduxjs/toolkit";
import { setCheckpoint, clearCheckpoint } from "../slices/sessionSlice";
import { ThunkApiType } from "../store";

export const createCheckpointThunk = createAsyncThunk<
  string,
  void,
  ThunkApiType
>(
  "session/createCheckpoint",
  async (_, { dispatch, extra }) => {
    const { ideMessenger } = extra;
    
    try {
      const result = await ideMessenger.request("createCheckpoint", undefined);
      if (result.status === "success") {
        dispatch(setCheckpoint({
          id: result.content,
          createdAt: Date.now()
        }));
        return result.content;
      } else {
        throw new Error(result.error || "Failed to create checkpoint");
      }
    } catch (error) {
      console.error("Failed to create checkpoint:", error);
      throw error;
    }
  }
);

export const rollbackToCheckpointThunk = createAsyncThunk<
  boolean,
  string,
  ThunkApiType
>(
  "session/rollbackToCheckpoint",
  async (checkpointId: string, { dispatch, extra }) => {
    const { ideMessenger } = extra;
    
    try {
      const result = await ideMessenger.request("rollbackToCheckpoint", { 
        checkpointId 
      });
      if (result.status === "success") {
        // 回滚成功后清除checkpoint状态
        dispatch(clearCheckpoint());
        return true;
      } else {
        throw new Error(result.error || "Failed to rollback to checkpoint");
      }
    } catch (error) {
      console.error("Failed to rollback to checkpoint:", error);
      throw error;
    }
  }
);
