package com.github.continuedev.continueintellijextension.services

import com.intellij.history.LocalHistory
import com.intellij.history.LocalHistoryAction
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * Service for managing checkpoints using IntelliJ's Local History
 */
@Service(Service.Level.PROJECT)
class CheckpointService(private val project: Project) {
    
    // Store checkpoint metadata
    private val checkpoints = ConcurrentHashMap<String, CheckpointInfo>()
    
    data class CheckpointInfo(
        val id: String,
        val timestamp: Long,
        val action: LocalHistoryAction?,
        val description: String,
        val affectedFiles: List<String>
    )
    
    /**
     * Create a new checkpoint
     */
    suspend fun createCheckpoint(description: String = "Continue Agent Checkpoint"): String {
        return withContext(Dispatchers.Default) {
            try {
                val timestamp = System.currentTimeMillis()
                val checkpointId = "continue_checkpoint_$timestamp"
                
                // Save all documents first
                ApplicationManager.getApplication().invokeAndWait {
                    FileDocumentManager.getInstance().saveAllDocuments()
                }
                
                // Get list of currently open files
                val openFiles = ApplicationManager.getApplication().runReadAction<List<String>> {
                    FileEditorManager.getInstance(project).openFiles.map { it.path }
                }
                
                // Create local history action
                val localHistory = LocalHistory.getInstance()
                val action = localHistory.startAction(description)
                
                // Store checkpoint info
                val checkpointInfo = CheckpointInfo(
                    id = checkpointId,
                    timestamp = timestamp,
                    action = action,
                    description = description,
                    affectedFiles = openFiles
                )
                
                checkpoints[checkpointId] = checkpointInfo
                
                // Finish the action to create the checkpoint
                action.finish()
                
                println("Created checkpoint: $checkpointId with ${openFiles.size} files")
                checkpointId
            } catch (e: Exception) {
                println("Error creating checkpoint: ${e.message}")
                throw e
            }
        }
    }
    
    /**
     * Rollback to a specific checkpoint
     */
    suspend fun rollbackToCheckpoint(checkpointId: String): Boolean {
        return withContext(Dispatchers.Default) {
            try {
                val checkpointInfo = checkpoints[checkpointId]
                    ?: throw IllegalArgumentException("Checkpoint not found: $checkpointId")
                
                ApplicationManager.getApplication().invokeLater {
                    try {
                        val localHistory = LocalHistory.getInstance()
                        val rollbackAction = localHistory.startAction("Continue Agent Rollback to ${checkpointInfo.description}")
                        
                        // Get all currently open files
                        val currentFiles = FileEditorManager.getInstance(project).openFiles
                        
                        // For each file, try to revert to the checkpoint time
                        for (file in currentFiles) {
                            try {
                                revertFileToTimestamp(file, checkpointInfo.timestamp)
                            } catch (e: Exception) {
                                println("Warning: Could not revert file ${file.path}: ${e.message}")
                            }
                        }
                        
                        rollbackAction.finish()
                        println("Successfully rolled back to checkpoint: $checkpointId")
                    } catch (e: Exception) {
                        println("Error during rollback: ${e.message}")
                        throw e
                    }
                }
                
                true
            } catch (e: Exception) {
                println("Error rolling back to checkpoint $checkpointId: ${e.message}")
                false
            }
        }
    }
    
    /**
     * Revert a specific file to a timestamp using local history
     */
    private fun revertFileToTimestamp(file: VirtualFile, timestamp: Long) {
        try {
            val localHistory = LocalHistory.getInstance()
            
            // Get revisions for this file
            val revisions = localHistory.getRevisionsFor(file)
            
            // Find the revision closest to our checkpoint timestamp
            val targetRevision = revisions.firstOrNull { revision ->
                revision.timestamp <= timestamp
            }
            
            if (targetRevision != null) {
                // Revert to this revision
                targetRevision.revertTo()
                println("Reverted ${file.path} to revision from ${targetRevision.timestamp}")
            } else {
                println("No suitable revision found for ${file.path} at timestamp $timestamp")
            }
        } catch (e: Exception) {
            println("Error reverting file ${file.path}: ${e.message}")
            throw e
        }
    }
    
    /**
     * Get information about a checkpoint
     */
    fun getCheckpointInfo(checkpointId: String): CheckpointInfo? {
        return checkpoints[checkpointId]
    }
    
    /**
     * List all available checkpoints
     */
    fun listCheckpoints(): List<CheckpointInfo> {
        return checkpoints.values.toList().sortedByDescending { it.timestamp }
    }
    
    /**
     * Remove a checkpoint
     */
    fun removeCheckpoint(checkpointId: String): Boolean {
        return checkpoints.remove(checkpointId) != null
    }
    
    /**
     * Clear all checkpoints
     */
    fun clearAllCheckpoints() {
        checkpoints.clear()
    }
}
