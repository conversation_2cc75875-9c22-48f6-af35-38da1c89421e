package com.github.continuedev.continueintellijextension.test

import com.github.continuedev.continueintellijextension.services.CheckpointService
import com.intellij.testFramework.fixtures.LightJavaCodeInsightFixtureTestCase
import kotlinx.coroutines.runBlocking

class CheckpointServiceTest : LightJavaCodeInsightFixtureTestCase() {
    
    private lateinit var checkpointService: CheckpointService
    
    override fun setUp() {
        super.setUp()
        checkpointService = CheckpointService(project)
    }
    
    fun testCreateCheckpoint() = runBlocking {
        // Test creating a checkpoint
        val checkpointId = checkpointService.createCheckpoint("Test Checkpoint")
        
        assertNotNull("Checkpoint ID should not be null", checkpointId)
        assertTrue("Checkpoint ID should start with continue_checkpoint_", 
                  checkpointId.startsWith("continue_checkpoint_"))
        
        // Verify checkpoint info is stored
        val checkpointInfo = checkpointService.getCheckpointInfo(checkpointId)
        assertNotNull("Checkpoint info should be stored", checkpointInfo)
        assertEquals("Test Checkpoint", checkpointInfo?.description)
    }
    
    fun testListCheckpoints() = runBlocking {
        // Create multiple checkpoints
        val checkpoint1 = checkpointService.createCheckpoint("Checkpoint 1")
        val checkpoint2 = checkpointService.createCheckpoint("Checkpoint 2")
        
        // List checkpoints
        val checkpoints = checkpointService.listCheckpoints()
        
        assertTrue("Should have at least 2 checkpoints", checkpoints.size >= 2)
        
        // Verify they are sorted by timestamp (newest first)
        for (i in 0 until checkpoints.size - 1) {
            assertTrue("Checkpoints should be sorted by timestamp", 
                      checkpoints[i].timestamp >= checkpoints[i + 1].timestamp)
        }
    }
    
    fun testRemoveCheckpoint() = runBlocking {
        // Create a checkpoint
        val checkpointId = checkpointService.createCheckpoint("Test Checkpoint")
        
        // Verify it exists
        assertNotNull(checkpointService.getCheckpointInfo(checkpointId))
        
        // Remove it
        val removed = checkpointService.removeCheckpoint(checkpointId)
        assertTrue("Should successfully remove checkpoint", removed)
        
        // Verify it's gone
        assertNull(checkpointService.getCheckpointInfo(checkpointId))
    }
    
    fun testClearAllCheckpoints() = runBlocking {
        // Create some checkpoints
        checkpointService.createCheckpoint("Checkpoint 1")
        checkpointService.createCheckpoint("Checkpoint 2")
        
        // Verify they exist
        assertTrue("Should have checkpoints", checkpointService.listCheckpoints().isNotEmpty())
        
        // Clear all
        checkpointService.clearAllCheckpoints()
        
        // Verify they're gone
        assertTrue("Should have no checkpoints", checkpointService.listCheckpoints().isEmpty())
    }
}
