# Checkpoint功能实现文档

## 概述

本文档描述了在IntelliJ IDEA插件中实现的checkpoint（检查点）功能，该功能允许用户在智能体会话开始时创建一个本地历史检查点，并在需要时回滚到该检查点。

## 架构设计

### 核心组件

1. **IntelliJIDE** - IDE接口实现，提供checkpoint API
2. **IdeProtocolClient** - 处理来自前端的checkpoint消息

### 技术实现

#### 简化设计原则

- 创建checkpoint时使用Git stash保存当前状态
- 回滚时查找并应用对应的Git stash
- 如果Git不可用，则使用时间戳作为fallback
- 不需要复杂的状态管理和存储

#### Git Stash集成

使用Git stash命令进行checkpoint管理：
- `git stash push -m "message"` - 创建带消息的stash
- `git stash list` - 列出所有stash
- `git stash apply stash@{index}` - 应用指定的stash
- 时间戳作为stash消息的一部分用于识别

## API接口

### 协议消息

#### createCheckpoint
- **请求**: 无参数
- **响应**: 时间戳字符串 (String)
- **功能**: 创建新的检查点，返回时间戳作为标识

#### rollbackToCheckpoint
- **请求**: `{ checkpointId: string }`
- **响应**: 无
- **功能**: 回滚到指定时间戳的检查点

### 数据结构

```kotlin
data class RollbackToCheckpointParams(
    val checkpointId: String  // 时间戳字符串
)
```

## 使用流程

### 创建检查点

1. 前端发送 `createCheckpoint` 消息
2. 插件保存所有打开的文档
3. 创建LocalHistory动作标记
4. 返回当前时间戳作为检查点ID

### 回滚操作

1. 前端发送 `rollbackToCheckpoint` 消息
2. 显示确认对话框
3. 用户确认后，解析时间戳
4. 遍历所有打开的文件
5. 使用LocalHistory API找到最接近时间戳的版本
6. 执行文件回滚操作
7. 显示操作结果

## 安全特性

1. **用户确认** - 回滚前显示确认对话框
2. **错误处理** - 完善的异常处理和错误提示
3. **文件保护** - 只回滚检查点时存在的文件
4. **操作日志** - 记录所有checkpoint操作

## 限制和注意事项

1. **本地历史依赖** - 依赖IDEA的本地历史功能
2. **文件范围** - 只回滚当前打开的文件
3. **时间精度** - 基于时间戳匹配，可能不够精确
4. **性能考虑** - 大量文件时回滚可能较慢

## 测试

### 手动测试

1. 在智能体模式下创建检查点
2. 修改一些文件
3. 点击回滚按钮
4. 验证文件是否恢复到检查点状态

## 优势

1. **简单直接** - 直接使用IDEA原生本地历史功能
2. **无额外存储** - 不需要额外的状态管理
3. **可靠性高** - 依赖IDEA成熟的本地历史机制
4. **用户熟悉** - 与IDEA原生功能一致

## 故障排除

### 常见问题

1. **检查点创建失败**
   - 检查文件权限
   - 确保本地历史功能已启用

2. **回滚不完整**
   - 检查文件是否在检查点创建时打开
   - 查看控制台日志了解具体错误

3. **性能问题**
   - 减少同时打开的文件数量
   - 检查磁盘空间是否充足
